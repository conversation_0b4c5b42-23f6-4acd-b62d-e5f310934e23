import Foundation

/// Basic product model for PLP (Product Listing Page)
struct Product: Codable, Identifiable, Equatable {
    let id: String
    let name: String
    let description: String
    let price: Price
    let imageUrl: String
    let category: Category
    let brand: String
    let rating: Double
    let reviewCount: Int
    let isInStock: Bool
    let tags: [String]
    
    static func == (lhs: Product, rhs: Product) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Detailed product model for PDP (Product Detail Page)
struct ProductDetail: Codable, Identifiable {
    let id: String
    let name: String
    let description: String
    let detailedDescription: String
    let price: Price
    let images: [ProductImage]
    let category: Category
    let brand: String
    let rating: Double
    let reviewCount: Int
    let isInStock: Bool
    let stockQuantity: Int
    let variants: [ProductVariant]
    let specifications: [ProductSpecification]
    let tags: [String]
    let relatedProducts: [String] // Product IDs
}

/// Product price information
struct Price: Codable, Equatable {
    let current: Double
    let original: Double?
    let currency: String
    let isOnSale: Bool
    
    var discountPercentage: Int? {
        guard let original = original, original > current else { return nil }
        return Int(((original - current) / original) * 100)
    }
    
    var formattedCurrent: String {
        return formatPrice(current)
    }
    
    var formattedOriginal: String? {
        guard let original = original else { return nil }
        return formatPrice(original)
    }
    
    private func formatPrice(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        return formatter.string(from: NSNumber(value: amount)) ?? "\(currency) \(amount)"
    }
}

/// Product category information
struct Category: Codable, Identifiable, Equatable {
    let id: String
    let name: String
    let parentId: String?
    let imageUrl: String?
    let level: Int
    
    static func == (lhs: Category, rhs: Category) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Product image information
struct ProductImage: Codable, Identifiable {
    let id: String
    let url: String
    let altText: String?
    let isPrimary: Bool
    let order: Int
}

/// Product variant (size, color, etc.)
struct ProductVariant: Codable, Identifiable {
    let id: String
    let type: VariantType
    let value: String
    let displayName: String
    let isAvailable: Bool
    let priceModifier: Double? // Additional cost for this variant
    let imageUrl: String?
}

/// Types of product variants
enum VariantType: String, Codable, CaseIterable {
    case size = "size"
    case color = "color"
    case material = "material"
    case style = "style"
    
    var displayName: String {
        switch self {
        case .size:
            return "Size"
        case .color:
            return "Color"
        case .material:
            return "Material"
        case .style:
            return "Style"
        }
    }
}

/// Product specification
struct ProductSpecification: Codable, Identifiable {
    let id: String
    let name: String
    let value: String
    let category: String
    let order: Int
}

/// Product review
struct ProductReview: Codable, Identifiable {
    let id: String
    let productId: String
    let userId: String
    let userName: String
    let rating: Int
    let title: String
    let comment: String
    let createdAt: Date
    let isVerifiedPurchase: Bool
    let helpfulCount: Int
}

/// API response wrapper for paginated product lists
struct ProductListResponse: Codable {
    let products: [Product]
    let pagination: PaginationInfo
    let filters: [ProductFilter]?
    let sortOptions: [SortOption]?
}

/// Pagination information
struct PaginationInfo: Codable {
    let currentPage: Int
    let totalPages: Int
    let totalItems: Int
    let itemsPerPage: Int
    let hasNextPage: Bool
    let hasPreviousPage: Bool
}

/// Product filter options
struct ProductFilter: Codable, Identifiable {
    let id: String
    let name: String
    let type: FilterType
    let options: [FilterOption]
}

enum FilterType: String, Codable {
    case range = "range"
    case multiSelect = "multi_select"
    case singleSelect = "single_select"
    case boolean = "boolean"
}

struct FilterOption: Codable, Identifiable {
    let id: String
    let value: String
    let displayName: String
    let count: Int?
}

/// Sort options for product listing
struct SortOption: Codable, Identifiable {
    let id: String
    let name: String
    let field: String
    let direction: SortDirection
}

enum SortDirection: String, Codable {
    case ascending = "asc"
    case descending = "desc"
}
