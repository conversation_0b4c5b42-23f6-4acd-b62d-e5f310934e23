import Foundation

/// Service for handling shopping cart operations
class CartService {
    private let apiClient: APIClient
    
    init(apiClient: APIClient = .shared) {
        self.apiClient = apiClient
    }
    
    /// Fetch current cart contents
    func fetchCart() async throws -> Cart {
        let endpoint = DecathlonAPI.cart
        return try await apiClient.request(endpoint: endpoint, responseType: Cart.self)
    }
    
    /// Add item to cart
    func addToCart(productId: String, variantId: String?, quantity: Int = 1) async throws -> Cart {
        let endpoint = DecathlonAPI.addToCart
        let request = AddToCartRequest(productId: productId, variantId: variantId, quantity: quantity)
        
        // Note: This is a simplified version. In the real implementation,
        // you would need to modify the APIClient to handle request bodies properly
        return try await apiClient.request(endpoint: endpoint, responseType: Cart.self)
    }
    
    /// Update cart item quantity
    func updateCartItem(itemId: String, quantity: Int) async throws -> Cart {
        let endpoint = DecathlonAPI.updateCartItem(itemId: itemId)
        let request = UpdateCartItemRequest(quantity: quantity)
        
        return try await apiClient.request(endpoint: endpoint, responseType: Cart.self)
    }
    
    /// Remove item from cart
    func removeFromCart(itemId: String) async throws -> Cart {
        let endpoint = DecathlonAPI.removeFromCart(itemId: itemId)
        try await apiClient.request(endpoint: endpoint)
        
        // Return updated cart
        return try await fetchCart()
    }
    
    /// Clear entire cart
    func clearCart() async throws {
        let endpoint = DecathlonAPI.clearCart
        try await apiClient.request(endpoint: endpoint)
    }
}

/// Cart model
struct Cart: Codable {
    let id: String
    let items: [CartItem]
    let subtotal: Double
    let tax: Double
    let shipping: Double
    let total: Double
    let currency: String
    let itemCount: Int
    let updatedAt: Date
    
    var isEmpty: Bool {
        return items.isEmpty
    }
    
    var formattedSubtotal: String {
        return formatPrice(subtotal)
    }
    
    var formattedTotal: String {
        return formatPrice(total)
    }
    
    private func formatPrice(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        return formatter.string(from: NSNumber(value: amount)) ?? "\(currency) \(amount)"
    }
}

/// Cart item model
struct CartItem: Codable, Identifiable {
    let id: String
    let productId: String
    let productName: String
    let productImageUrl: String
    let variantId: String?
    let variantDisplayName: String?
    let price: Double
    let quantity: Int
    let subtotal: Double
    let isInStock: Bool
    let maxQuantity: Int?
    
    var formattedPrice: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        return formatter.string(from: NSNumber(value: price)) ?? "\(price)"
    }
    
    var formattedSubtotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        return formatter.string(from: NSNumber(value: subtotal)) ?? "\(subtotal)"
    }
}

/// Request models for cart operations
struct AddToCartRequest: Codable {
    let productId: String
    let variantId: String?
    let quantity: Int
}

struct UpdateCartItemRequest: Codable {
    let quantity: Int
}
