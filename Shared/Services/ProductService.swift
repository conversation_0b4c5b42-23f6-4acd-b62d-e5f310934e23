import Foundation

/// Service for handling product-related API operations
class ProductService {
    private let apiClient: APIClient
    
    init(apiClient: APIClient = .shared) {
        self.apiClient = apiClient
    }
    
    /// Fetch products for PLP with pagination
    func fetchProducts(page: Int = 1, limit: Int = 20) async throws -> ProductListResponse {
        let endpoint = DecathlonAPI.products(page: page, limit: limit)
        return try await apiClient.request(endpoint: endpoint, responseType: ProductListResponse.self)
    }
    
    /// Search products with query
    func searchProducts(query: String, page: Int = 1, limit: Int = 20) async throws -> ProductListResponse {
        let endpoint = DecathlonAPI.productSearch(query: query, page: page, limit: limit)
        return try await apiClient.request(endpoint: endpoint, responseType: ProductListResponse.self)
    }
    
    /// Fetch products by category
    func fetchProductsByCategory(categoryId: String, page: Int = 1, limit: Int = 20) async throws -> ProductListResponse {
        let endpoint = DecathlonAPI.productsByCategory(categoryId: categoryId, page: page, limit: limit)
        return try await apiClient.request(endpoint: endpoint, responseType: ProductListResponse.self)
    }
    
    /// Fetch detailed product information for PDP
    func fetchProductDetail(productId: String) async throws -> ProductDetail {
        let endpoint = DecathlonAPI.productDetail(id: productId)
        return try await apiClient.request(endpoint: endpoint, responseType: ProductDetail.self)
    }
    
    /// Fetch product variants
    func fetchProductVariants(productId: String) async throws -> [ProductVariant] {
        let endpoint = DecathlonAPI.productVariants(productId: productId)
        return try await apiClient.request(endpoint: endpoint, responseType: [ProductVariant].self)
    }
    
    /// Fetch product reviews
    func fetchProductReviews(productId: String, page: Int = 1, limit: Int = 10) async throws -> ProductReviewResponse {
        let endpoint = DecathlonAPI.productReviews(productId: productId, page: page, limit: limit)
        return try await apiClient.request(endpoint: endpoint, responseType: ProductReviewResponse.self)
    }
    
    /// Fetch product recommendations
    func fetchProductRecommendations(productId: String) async throws -> [Product] {
        let endpoint = DecathlonAPI.productRecommendations(productId: productId)
        return try await apiClient.request(endpoint: endpoint, responseType: [Product].self)
    }
    
    /// Fetch all categories
    func fetchCategories() async throws -> [Category] {
        let endpoint = DecathlonAPI.categories
        return try await apiClient.request(endpoint: endpoint, responseType: [Category].self)
    }
    
    /// Fetch category details
    func fetchCategoryDetail(categoryId: String) async throws -> Category {
        let endpoint = DecathlonAPI.categoryDetail(id: categoryId)
        return try await apiClient.request(endpoint: endpoint, responseType: Category.self)
    }
}

/// Response wrapper for product reviews
struct ProductReviewResponse: Codable {
    let reviews: [ProductReview]
    let pagination: PaginationInfo
    let averageRating: Double
    let ratingDistribution: [Int: Int] // Rating (1-5) to count mapping
}
