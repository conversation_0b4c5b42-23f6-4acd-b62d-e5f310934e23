import Foundation

/// HTTP methods supported by the API
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

/// API endpoint configuration
protocol APIEndpoint {
    var baseURL: String { get }
    var path: String { get }
    var method: HTTPMethod { get }
    var headers: [String: String]? { get }
    var queryParameters: [String: Any]? { get }
    var body: Data? { get }
}

extension APIEndpoint {
    /// Default headers for all requests
    var headers: [String: String]? {
        return [
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]
    }
    
    /// Default query parameters
    var queryParameters: [String: Any]? {
        return nil
    }
    
    /// Default body
    var body: Data? {
        return nil
    }
    
    /// Constructs the full URL for the endpoint
    var url: URL? {
        guard var urlComponents = URLComponents(string: baseURL + path) else {
            return nil
        }
        
        if let queryParameters = queryParameters {
            urlComponents.queryItems = queryParameters.map { key, value in
                URLQueryItem(name: key, value: "\(value)")
            }
        }
        
        return urlComponents.url
    }
}

/// Environment configuration for different deployment stages
enum APIEnvironment {
    case development
    case staging
    case production
    
    var baseURL: String {
        switch self {
        case .development:
            return "https://dev-api.decathlon.com"
        case .staging:
            return "https://staging-api.decathlon.com"
        case .production:
            return "https://api.decathlon.com"
        }
    }
}

/// Decathlon API endpoints
enum DecathlonAPI: APIEndpoint {
    // Product endpoints
    case products(page: Int, limit: Int)
    case productDetail(id: String)
    case productSearch(query: String, page: Int, limit: Int)
    case productsByCategory(categoryId: String, page: Int, limit: Int)
    case productVariants(productId: String)
    case productReviews(productId: String, page: Int, limit: Int)
    case productRecommendations(productId: String)
    
    // Category endpoints
    case categories
    case categoryDetail(id: String)
    
    // Cart endpoints
    case cart
    case addToCart
    case updateCartItem(itemId: String)
    case removeFromCart(itemId: String)
    case clearCart
    
    // Order endpoints
    case orders(page: Int, limit: Int)
    case orderDetail(id: String)
    case createOrder
    case cancelOrder(id: String)
    case orderTracking(id: String)
    
    var baseURL: String {
        // This should be configurable based on build configuration
        return APIEnvironment.development.baseURL
    }
    
    var path: String {
        switch self {
        case .products:
            return "/api/v1/products"
        case .productDetail(let id):
            return "/api/v1/products/\(id)"
        case .productSearch:
            return "/api/v1/products/search"
        case .productsByCategory(let categoryId, _, _):
            return "/api/v1/categories/\(categoryId)/products"
        case .productVariants(let productId):
            return "/api/v1/products/\(productId)/variants"
        case .productReviews(let productId, _, _):
            return "/api/v1/products/\(productId)/reviews"
        case .productRecommendations(let productId):
            return "/api/v1/products/\(productId)/recommendations"
        case .categories:
            return "/api/v1/categories"
        case .categoryDetail(let id):
            return "/api/v1/categories/\(id)"
        case .cart:
            return "/api/v1/cart"
        case .addToCart:
            return "/api/v1/cart/items"
        case .updateCartItem(let itemId):
            return "/api/v1/cart/items/\(itemId)"
        case .removeFromCart(let itemId):
            return "/api/v1/cart/items/\(itemId)"
        case .clearCart:
            return "/api/v1/cart"
        case .orders:
            return "/api/v1/orders"
        case .orderDetail(let id):
            return "/api/v1/orders/\(id)"
        case .createOrder:
            return "/api/v1/orders"
        case .cancelOrder(let id):
            return "/api/v1/orders/\(id)/cancel"
        case .orderTracking(let id):
            return "/api/v1/orders/\(id)/tracking"
        }
    }
    
    var method: HTTPMethod {
        switch self {
        case .products, .productDetail, .productSearch, .productsByCategory,
             .productVariants, .productReviews, .productRecommendations,
             .categories, .categoryDetail, .cart, .orders, .orderDetail, .orderTracking:
            return .GET
        case .addToCart, .createOrder:
            return .POST
        case .updateCartItem, .cancelOrder:
            return .PUT
        case .removeFromCart, .clearCart:
            return .DELETE
        }
    }
    
    var queryParameters: [String: Any]? {
        switch self {
        case .products(let page, let limit),
             .productsByCategory(_, let page, let limit),
             .productReviews(_, let page, let limit),
             .orders(let page, let limit):
            return ["page": page, "limit": limit]
        case .productSearch(let query, let page, let limit):
            return ["q": query, "page": page, "limit": limit]
        default:
            return nil
        }
    }
}
