import Foundation
import os.log

/// Protocol for network middleware
protocol NetworkMiddleware {
    func processRequest(_ request: URLRequest) -> URLRequest
    func processResponse(_ response: URLResponse?, data: Data?, error: Error?) -> (URLResponse?, Data?, Error?)
}

/// Logging middleware for debugging network requests and responses
class LoggingMiddleware: NetworkMiddleware {
    private let logger = Logger(subsystem: "com.decathlon.shopping", category: "networking")
    private let isDebugMode: Bool
    
    init(isDebugMode: Bool = true) {
        self.isDebugMode = isDebugMode
    }
    
    func processRequest(_ request: URLRequest) -> URLRequest {
        guard isDebugMode else { return request }
        
        logger.info("🚀 REQUEST: \(request.httpMethod ?? "UNKNOWN") \(request.url?.absoluteString ?? "Unknown URL")")
        
        // Log headers
        if let headers = request.allHTTPHeaderFields, !headers.isEmpty {
            logger.debug("📋 Headers: \(headers)")
        }
        
        // Log body
        if let body = request.httpBody,
           let bodyString = String(data: body, encoding: .utf8) {
            logger.debug("📦 Body: \(bodyString)")
        }
        
        return request
    }
    
    func processResponse(_ response: URLResponse?, data: Data?, error: Error?) -> (URLResponse?, Data?, Error?) {
        guard isDebugMode else { return (response, data, error) }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        if let httpResponse = response as? HTTPURLResponse {
            let statusCode = httpResponse.statusCode
            let url = httpResponse.url?.absoluteString ?? "Unknown URL"
            
            if statusCode >= 200 && statusCode < 300 {
                logger.info("✅ RESPONSE: \(statusCode) \(url)")
            } else {
                logger.error("❌ RESPONSE: \(statusCode) \(url)")
            }
            
            // Log response headers
            if !httpResponse.allHeaderFields.isEmpty {
                logger.debug("📋 Response Headers: \(httpResponse.allHeaderFields)")
            }
        }
        
        // Log response data
        if let data = data {
            logger.debug("📦 Response Size: \(data.count) bytes")
            
            if let responseString = String(data: data, encoding: .utf8) {
                // Truncate long responses for readability
                let truncatedResponse = responseString.count > 1000 
                    ? String(responseString.prefix(1000)) + "... (truncated)"
                    : responseString
                logger.debug("📄 Response Data: \(truncatedResponse)")
            }
        }
        
        // Log errors
        if let error = error {
            logger.error("💥 Error: \(error.localizedDescription)")
        }
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = (endTime - startTime) * 1000 // Convert to milliseconds
        logger.debug("⏱️ Request Duration: \(String(format: "%.2f", duration))ms")
        
        return (response, data, error)
    }
}

/// Analytics middleware for tracking API usage
class AnalyticsMiddleware: NetworkMiddleware {
    private let logger = Logger(subsystem: "com.decathlon.shopping", category: "analytics")
    
    func processRequest(_ request: URLRequest) -> URLRequest {
        // Track API call initiation
        let endpoint = request.url?.path ?? "unknown"
        let method = request.httpMethod ?? "unknown"
        
        logger.info("📊 API Call Started: \(method) \(endpoint)")
        
        // Here you could integrate with analytics services like Firebase, Mixpanel, etc.
        // trackEvent("api_call_started", parameters: ["endpoint": endpoint, "method": method])
        
        return request
    }
    
    func processResponse(_ response: URLResponse?, data: Data?, error: Error?) -> (URLResponse?, Data?, Error?) {
        if let httpResponse = response as? HTTPURLResponse {
            let endpoint = httpResponse.url?.path ?? "unknown"
            let statusCode = httpResponse.statusCode
            
            if error != nil {
                logger.error("📊 API Call Failed: \(endpoint) - \(statusCode)")
                // trackEvent("api_call_failed", parameters: ["endpoint": endpoint, "status_code": statusCode])
            } else {
                logger.info("📊 API Call Succeeded: \(endpoint) - \(statusCode)")
                // trackEvent("api_call_succeeded", parameters: ["endpoint": endpoint, "status_code": statusCode])
            }
        }
        
        return (response, data, error)
    }
}

/// Authentication middleware for injecting auth tokens
class AuthenticationMiddleware: NetworkMiddleware {
    private var authToken: String?
    
    func setAuthToken(_ token: String?) {
        self.authToken = token
    }
    
    func processRequest(_ request: URLRequest) -> URLRequest {
        var mutableRequest = request
        
        // Inject authentication token if available
        if let token = authToken {
            mutableRequest.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // Add user agent
        let userAgent = "DecathlonShoppingApp/1.0 iOS/\(UIDevice.current.systemVersion)"
        mutableRequest.setValue(userAgent, forHTTPHeaderField: "User-Agent")
        
        return mutableRequest
    }
    
    func processResponse(_ response: URLResponse?, data: Data?, error: Error?) -> (URLResponse?, Data?, Error?) {
        // Handle authentication errors
        if let httpResponse = response as? HTTPURLResponse,
           httpResponse.statusCode == 401 {
            // Clear invalid token
            authToken = nil
            
            // You could trigger a token refresh here
            // NotificationCenter.default.post(name: .authTokenExpired, object: nil)
        }
        
        return (response, data, error)
    }
}

/// Middleware manager to coordinate multiple middleware
class MiddlewareManager {
    var middlewares: [NetworkMiddleware] = []
    
    func addMiddleware(_ middleware: NetworkMiddleware) {
        middlewares.append(middleware)
    }
    
    func processRequest(_ request: URLRequest) -> URLRequest {
        return middlewares.reduce(request) { result, middleware in
            middleware.processRequest(result)
        }
    }
    
    func processResponse(_ response: URLResponse?, data: Data?, error: Error?) -> (URLResponse?, Data?, Error?) {
        return middlewares.reduce((response, data, error)) { result, middleware in
            middleware.processResponse(result.0, data: result.1, error: result.2)
        }
    }
}
