import Foundation

/// Generic API client for handling REST API requests with async/await
class APIClient {
    static let shared = APIClient()
    
    private let session: URLSession
    private let middlewareManager: MiddlewareManager
    private let decoder: JSONDecoder
    private let encoder: J<PERSON>NEncoder
    
    private init() {
        // Configure URLSession
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30.0
        configuration.timeoutIntervalForResource = 60.0
        configuration.waitsForConnectivity = true
        
        self.session = URLSession(configuration: configuration)
        
        // Setup middleware
        self.middlewareManager = MiddlewareManager()
        self.middlewareManager.addMiddleware(LoggingMiddleware())
        self.middlewareManager.addMiddleware(AnalyticsMiddleware())
        self.middlewareManager.addMiddleware(AuthenticationMiddleware())
        
        // Configure JSON decoder/encoder
        self.decoder = JSONDecoder()
        self.decoder.dateDecodingStrategy = .iso8601
        self.decoder.keyDecodingStrategy = .convertFromSnakeCase
        
        self.encoder = JSONEncoder()
        self.encoder.dateEncodingStrategy = .iso8601
        self.encoder.keyEncodingStrategy = .convertToSnakeCase
    }
    
    /// Set authentication token for all requests
    func setAuthToken(_ token: String?) {
        // Find and update authentication middleware
        for middleware in middlewareManager.middlewares {
            if let authMiddleware = middleware as? AuthenticationMiddleware {
                authMiddleware.setAuthToken(token)
            }
        }
    }

    /// Access to middleware manager for adding custom middleware
    var middleware: MiddlewareManager {
        return middlewareManager
    }
    
    /// Generic method to perform API requests
    func request<T: Codable>(
        endpoint: APIEndpoint,
        responseType: T.Type
    ) async throws -> T {
        guard let url = endpoint.url else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = endpoint.method.rawValue
        request.httpBody = endpoint.body
        
        // Add headers
        endpoint.headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // Process request through middleware
        request = middlewareManager.processRequest(request)
        
        do {
            let (data, response) = try await session.data(for: request)
            
            // Process response through middleware
            let (processedResponse, processedData, processedError) = middlewareManager.processResponse(response, data: data, error: nil)
            
            if let error = processedError {
                throw error
            }
            
            guard let httpResponse = processedResponse as? HTTPURLResponse else {
                throw APIError.unknown(NSError(domain: "Invalid response type", code: 0))
            }
            
            // Handle HTTP status codes
            try handleHTTPResponse(httpResponse, data: processedData)
            
            guard let responseData = processedData else {
                throw APIError.noData
            }
            
            // Decode response
            do {
                let decodedResponse = try decoder.decode(responseType, from: responseData)
                return decodedResponse
            } catch {
                throw APIError.decodingError(error.localizedDescription)
            }
            
        } catch let urlError as URLError {
            let (_, _, processedError) = middlewareManager.processResponse(nil, data: nil, error: urlError)
            
            if let processedError = processedError {
                throw processedError
            }
            
            throw APIError.networkError(urlError)
        } catch {
            throw error
        }
    }
    
    /// Perform request without expecting a response body (for DELETE, etc.)
    func request(endpoint: APIEndpoint) async throws {
        guard let url = endpoint.url else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = endpoint.method.rawValue
        request.httpBody = endpoint.body
        
        // Add headers
        endpoint.headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // Process request through middleware
        request = middlewareManager.processRequest(request)
        
        do {
            let (data, response) = try await session.data(for: request)
            
            // Process response through middleware
            let (processedResponse, _, processedError) = middlewareManager.processResponse(response, data: data, error: nil)
            
            if let error = processedError {
                throw error
            }
            
            guard let httpResponse = processedResponse as? HTTPURLResponse else {
                throw APIError.unknown(NSError(domain: "Invalid response type", code: 0))
            }
            
            // Handle HTTP status codes
            try handleHTTPResponse(httpResponse, data: data)
            
        } catch let urlError as URLError {
            let (_, _, processedError) = middlewareManager.processResponse(nil, data: nil, error: urlError)
            
            if let processedError = processedError {
                throw processedError
            }
            
            throw APIError.networkError(urlError)
        } catch {
            throw error
        }
    }
    
    /// Handle HTTP response status codes
    private func handleHTTPResponse(_ response: HTTPURLResponse, data: Data?) throws {
        switch response.statusCode {
        case 200...299:
            // Success - do nothing
            break
        case 400:
            let message = extractErrorMessage(from: data) ?? "Bad Request"
            throw APIError.httpError(statusCode: 400, message: message)
        case 401:
            throw APIError.unauthorized
        case 403:
            throw APIError.forbidden
        case 404:
            throw APIError.notFound
        case 408:
            throw APIError.timeout
        case 500...599:
            let message = extractErrorMessage(from: data) ?? "Server Error"
            throw APIError.serverError(message: message)
        default:
            let message = extractErrorMessage(from: data) ?? "Unknown Error"
            throw APIError.httpError(statusCode: response.statusCode, message: message)
        }
    }
    
    /// Extract error message from response data
    private func extractErrorMessage(from data: Data?) -> String? {
        guard let data = data else { return nil }
        
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                return json["message"] as? String ?? json["error"] as? String
            }
        } catch {
            // If JSON parsing fails, try to get string representation
            return String(data: data, encoding: .utf8)
        }
        
        return nil
    }
}

/// Extension to add convenience methods for common request patterns
extension APIClient {
    /// POST request with body encoding
    func post<T: Codable, R: Codable>(
        endpoint: APIEndpoint,
        body: T,
        responseType: R.Type
    ) async throws -> R {
        var mutableEndpoint = endpoint
        
        do {
            let bodyData = try encoder.encode(body)
            // Create a new endpoint with the encoded body
            // Note: This is a simplified approach. In a real implementation,
            // you might want to create a mutable endpoint protocol
        } catch {
            throw APIError.encodingError(error.localizedDescription)
        }
        
        return try await request(endpoint: mutableEndpoint, responseType: responseType)
    }
    
    /// PUT request with body encoding
    func put<T: Codable, R: Codable>(
        endpoint: APIEndpoint,
        body: T,
        responseType: R.Type
    ) async throws -> R {
        // Similar implementation to POST
        return try await post(endpoint: endpoint, body: body, responseType: responseType)
    }
}
