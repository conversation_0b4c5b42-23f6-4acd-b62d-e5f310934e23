import Foundation
import Combine

/// Base protocol for all ViewStates following the View-ViewState pattern
protocol ViewState: ObservableObject {
    associatedtype LoadingState
    associatedtype ErrorState: Error
    
    var isLoading: Bool { get }
    var error: ErrorState? { get }
    
    func handleError(_ error: Error)
    func clearError()
}

/// Generic loading states for different operations
enum LoadingState: Equatable {
    case idle
    case loading
    case refreshing
    case loadingMore
    
    var isLoading: Bool {
        switch self {
        case .idle:
            return false
        case .loading, .refreshing, .loadingMore:
            return true
        }
    }
}

/// Base ViewState implementation with common functionality
class BaseViewState: ObservableObject {
    @Published var loadingState: LoadingState = .idle
    @Published var error: APIError?
    
    var isLoading: Bool {
        return loadingState.isLoading
    }
    
    /// Handle API errors and convert them to appropriate error states
    func handleError(_ error: Error) {
        DispatchQueue.main.async {
            if let apiError = error as? APIError {
                self.error = apiError
            } else {
                self.error = .unknown(error)
            }
            self.loadingState = .idle
        }
    }
    
    /// Clear current error state
    func clearError() {
        DispatchQueue.main.async {
            self.error = nil
        }
    }
    
    /// Set loading state
    func setLoading(_ state: LoadingState) {
        DispatchQueue.main.async {
            self.loadingState = state
            if state.isLoading {
                self.error = nil
            }
        }
    }
    
    /// Execute async operation with loading state management
    @MainActor
    func executeAsync<T>(
        loadingState: LoadingState = .loading,
        operation: @escaping () async throws -> T
    ) async -> T? {
        setLoading(loadingState)
        
        do {
            let result = try await operation()
            setLoading(.idle)
            return result
        } catch {
            handleError(error)
            return nil
        }
    }
    
    /// Execute async operation without return value
    @MainActor
    func executeAsync(
        loadingState: LoadingState = .loading,
        operation: @escaping () async throws -> Void
    ) async {
        setLoading(loadingState)
        
        do {
            try await operation()
            setLoading(.idle)
        } catch {
            handleError(error)
        }
    }
}

/// ViewState for paginated data
class PaginatedViewState<T>: BaseViewState {
    @Published var items: [T] = []
    @Published var hasMorePages: Bool = true
    @Published var currentPage: Int = 1
    
    private let itemsPerPage: Int
    
    init(itemsPerPage: Int = 20) {
        self.itemsPerPage = itemsPerPage
        super.init()
    }
    
    /// Load first page of data
    @MainActor
    func loadInitialData(operation: @escaping (Int, Int) async throws -> PaginatedResponse<T>) async {
        currentPage = 1
        items = []
        hasMorePages = true
        
        await executeAsync(loadingState: .loading) {
            let response = try await operation(self.currentPage, self.itemsPerPage)
            self.items = response.items
            self.hasMorePages = response.hasMorePages
        }
    }
    
    /// Refresh current data
    @MainActor
    func refresh(operation: @escaping (Int, Int) async throws -> PaginatedResponse<T>) async {
        let savedPage = currentPage
        currentPage = 1
        
        await executeAsync(loadingState: .refreshing) {
            let response = try await operation(1, savedPage * self.itemsPerPage)
            self.items = response.items
            self.hasMorePages = response.hasMorePages
        }
    }
    
    /// Load next page of data
    @MainActor
    func loadMore(operation: @escaping (Int, Int) async throws -> PaginatedResponse<T>) async {
        guard hasMorePages && !isLoading else { return }
        
        let nextPage = currentPage + 1
        
        await executeAsync(loadingState: .loadingMore) {
            let response = try await operation(nextPage, self.itemsPerPage)
            self.items.append(contentsOf: response.items)
            self.hasMorePages = response.hasMorePages
            self.currentPage = nextPage
        }
    }
    
    /// Clear all data
    func clearData() {
        DispatchQueue.main.async {
            self.items = []
            self.currentPage = 1
            self.hasMorePages = true
            self.loadingState = .idle
            self.error = nil
        }
    }
}

/// Generic paginated response wrapper
struct PaginatedResponse<T> {
    let items: [T]
    let hasMorePages: Bool
    let totalItems: Int?
    let currentPage: Int
    
    init(items: [T], hasMorePages: Bool, totalItems: Int? = nil, currentPage: Int) {
        self.items = items
        self.hasMorePages = hasMorePages
        self.totalItems = totalItems
        self.currentPage = currentPage
    }
}

/// Extension to convert API responses to PaginatedResponse
extension ProductListResponse {
    func toPaginatedResponse() -> PaginatedResponse<Product> {
        return PaginatedResponse(
            items: products,
            hasMorePages: pagination.hasNextPage,
            totalItems: pagination.totalItems,
            currentPage: pagination.currentPage
        )
    }
}
