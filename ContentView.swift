import SwiftUI

struct ContentView: View {
    var body: some View {
        TabView {
             ()
                .tabItem {
                    Image(systemName: "list.bullet")
                    Text("Products")
                }
            
            CartView()
                .tabItem {
                    Image(systemName: "cart")
                    Text("Cart")
                }
            
            ProfileView()
                .tabItem {
                    Image(systemName: "person")
                    Text("Profile")
                }
        }
    }
}

// Placeholder views - will be implemented later
struct PLPView: View {
    var body: some View {
        NavigationView {
            Text("Product Listing Page")
                .navigationTitle("Decathlon")
        }
    }
}

struct CartView: View {
    var body: some View {
        NavigationView {
            Text("Shopping Cart")
                .navigationTitle("Cart")
        }
    }
}

struct ProfileView: View {
    var body: some View {
        NavigationView {
            Text("User Profile")
                .navigationTitle("Profile")
        }
    }
}

#Preview {
    ContentView()
}
