# Decathlon Shopping App

A native iOS ecommerce application built with SwiftUI using the View-ViewState architecture pattern.

## Architecture

### View-ViewState Pattern
- **Views**: Pure SwiftUI views that render UI based on ViewState
- **ViewStates**: Observable objects that hold UI state and business logic
- **Services**: API clients and business logic services
- **Models**: Data models and business entities

### Project Structure
```
DecathlonShoppingApp/
├── App/
│   ├── DecathlonShoppingApp.swift    # Main app entry point
│   └── ContentView.swift             # Root view with tab navigation
├── Features/
│   ├── PLP/                          # Product Listing Page
│   ├── PDP/                          # Product Detail Page
│   ├── ShoppingCart/                 # Shopping cart functionality
│   ├── UserProfile/                  # User profile and account
│   └── Orders/                       # Order management
├── Shared/
│   ├── Networking/                   # API client and networking layer
│   ├── Services/                     # Business logic services
│   ├── Models/                       # Data models
│   ├── ViewStates/                   # Base ViewState implementations
│   ├── Extensions/                   # Swift/SwiftUI extensions
│   └── Components/                   # Reusable UI components
└── Resources/
    ├── Assets.xcassets              # App assets
    └── Localizable.strings          # Localization strings
```

## Technical Requirements

- **Platform**: iOS 15.0+
- **Framework**: SwiftUI (native)
- **Architecture**: View-ViewState pattern
- **Networking**: Native URLSession with async/await
- **API**: REST APIs with JSON responses

## Networking Layer

### Features
- ✅ Native async/await support
- ✅ Generic REST API client
- ✅ Comprehensive error handling
- ✅ Request/Response middleware system
- ✅ Logging and debugging support
- ✅ Analytics tracking middleware
- ✅ Authentication token management

### API Endpoints
- **Products**: Listing, search, details, variants, reviews
- **Categories**: Category browsing and filtering
- **Cart**: Add, update, remove items
- **Orders**: Create, track, manage orders

### Error Handling
The networking layer provides comprehensive error handling for:
- Network connectivity issues
- HTTP status codes (4xx, 5xx)
- JSON parsing errors
- Authentication errors
- Timeout handling

### Middleware System
- **LoggingMiddleware**: Request/response logging for debugging
- **AnalyticsMiddleware**: API usage tracking
- **AuthenticationMiddleware**: Token injection and management

## ViewState Pattern

### Base Classes
- `BaseViewState`: Common loading and error state management
- `PaginatedViewState`: Specialized for paginated data (PLP, reviews, etc.)

### Loading States
- `idle`: No operation in progress
- `loading`: Initial data loading
- `refreshing`: Pull-to-refresh operation
- `loadingMore`: Pagination loading

## Services

### ProductService
- Product listing with pagination
- Product search functionality
- Product detail retrieval
- Category management
- Product reviews and recommendations

### CartService
- Cart management (add, update, remove items)
- Cart persistence
- Checkout preparation

## Models

### Core Models
- `Product`: Basic product information for listings
- `ProductDetail`: Detailed product information for PDP
- `Category`: Product categorization
- `Cart` & `CartItem`: Shopping cart models
- `ProductVariant`: Product variations (size, color, etc.)

### API Response Models
- `ProductListResponse`: Paginated product listings
- `PaginationInfo`: Pagination metadata
- `ProductFilter`: Filtering options

## Next Steps

1. **Implement PLP (Product Listing Page)**
   - Create PLPViewState with product loading
   - Build product grid UI with SwiftUI
   - Add search and filtering functionality

2. **Implement PDP (Product Detail Page)**
   - Create PDPViewState for product details
   - Build product detail UI with image carousel
   - Add variant selection and add-to-cart functionality

3. **Implement Shopping Cart**
   - Create cart ViewState and UI
   - Add cart persistence
   - Implement checkout flow

4. **Add Testing**
   - Unit tests for ViewStates
   - Service layer testing
   - UI testing with SwiftUI

## Development Guidelines

- Use native SwiftUI components and modifiers
- Follow View-ViewState pattern consistently
- Handle loading and error states properly
- Use async/await for all network operations
- Implement proper error handling and user feedback
- Follow iOS Human Interface Guidelines
